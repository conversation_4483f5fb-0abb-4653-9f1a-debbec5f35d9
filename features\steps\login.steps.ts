import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { getEnv, loginToNgApp } from '../support/helpers';

Given('access to GRO login page {string}', async function (url) {
    if (!this.page) {
        await this.init();
    }
    await this.page.goto(url);
});

When('user enters valid credentials', async function () {
    // Get credentials from environment
    const username = getEnv('username');
    const password = getEnv('password');

    // Fill in login form
    await this.page.locator('input[formcontrolname="username"]').fill(username);
    await this.page.locator('input[formcontrolname="password"]').fill(password);

    // Click login button
    await this.page.locator('button[type="submit"]').click();
});

// Reusable step for other features to use
Given('user is logged in to GRO', async function () {
    if (!this.page) {
        await this.init();
    }

    // Use the helper function to login
    await loginToNgApp(
        this.page,
        getEnv('username'),
        getEnv('password')
    );
});

Then('user should be logged in successfully', async function () {
    // Wait for navigation to complete
    await this.page.waitForURL('**/home');

    // Check if user is logged in by looking for a common element on the home page
    const userMenuElement = this.page.locator('.navMenu');
    await expect(userMenuElement).toBeVisible();
});

Then('user should see the home page', async function () {
    // Verify we're on the home page
    await expect(this.page).toHaveURL(new RegExp('groNg/#/home'));

    // Check for a specific element on the home page
    const headerElement = this.page.locator('h2[class=ng-star-inserted]');
    await expect(headerElement).toHaveText('Additional Tools & Resources');
});

When('user enters invalid credentials', async function () {
    // Use invalid credentials
    await this.page.locator('input[formcontrolname="username"]').fill('invalid_user');
    await this.page.locator('input[formcontrolname="password"]').fill('invalid_password');

    // Click login button
    await this.page.locator('button[type="submit"]').click();
});

Then('user should see an error message', async function () {
    // Check for error message
    const errorMessage = this.page.locator('.error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText(/invalid|failed|incorrect/i);
});
