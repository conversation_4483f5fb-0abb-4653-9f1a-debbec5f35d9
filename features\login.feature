Feature: Login to GRO
	@LoginSanity @SanityTest @Smoke
	Scenario: User logs in to GRO application
    Given access to GRO login page "/groNg/#/login"
    When user enters valid credentials
    Then user should be logged in successfully
    And user should see the home page

	@LoginFailure @Regression
	Scenario: User attempts to login with invalid credentials
    Given access to GRO login page "/groNg/#/login"
    When user enters invalid credentials
    Then user should see an error message
