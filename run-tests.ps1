# PowerShell script to run tests with specific environment

param (
    [string]$env = "dev",
    [string]$browser = "chromium",
    [string]$tags = "",
    [switch]$parallel = $false,
    [int]$workers = 3,
    [switch]$report = $false,
    [switch]$openReport = $false
)

# Set environment variable
$env:TEST_ENV = $env

# Build the command
$profileName = $browser
if ($parallel) {
    $profileName = "parallel-$browser"

    # Override the number of workers if specified
    if ($workers -ne 3) {
        $env:CUCUMBER_PARALLEL = $workers
    }
}

$command = "npx cucumber-js --profile $profileName"

# Add tags if specified
if ($tags -ne "") {
    $command += " --tags `"$tags`""
}

# Display the command
Write-Host "Running: $command"
Write-Host "Environment: $env"
Write-Host "Browser: $browser"
Write-Host "Parallel: $parallel"
if ($parallel) {
    Write-Host "Workers: $workers"
}

# Execute the command
Invoke-Expression $command

# Generate reports if requested
if ($report) {
    Write-Host "Generating HTML reports..."
    Invoke-Expression "npx ts-node generate-reports.ts"

    # Open the report if requested
    if ($openReport) {
        Write-Host "Opening HTML report..."
        if ($parallel) {
            Invoke-Expression "start reports/cucumber-parallel-$browser-report-html-report.html"
        } else {
            Invoke-Expression "start reports/cucumber-$browser-report-html-report.html"
        }
    }
}
