import { test, expect } from '@playwright/test';
import { loginWithWebId } from '../features/support/helpers';

// This file contains Playwright tests that can be run in UI mode
// These tests mirror the functionality in the Cucumber features

test.describe('Login Tests', () => {
  test('User can login with valid credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');
    
    // Get credentials from environment
    const username = 'rayth11';
    const password = 'Testtest2';
    
    // Fill in login form
    await page.locator('input[formcontrolname="username"]').fill(username);
    await page.locator('input[formcontrolname="password"]').fill(password);
    
    // Click login button
    await page.locator('button[type="submit"]').click();
    
    // Wait for navigation to complete
    await page.waitForURL('**/home');
    
    // Check if user is logged in by looking for a common element on the home page
    const userMenuElement = page.locator('.navMenu');
    await expect(userMenuElement).toBeVisible();
    
    // Verify we're on the home page
    await expect(page).toHaveURL(new RegExp('groNg/#/home'));
    
    // Check for a specific element on the home page
    const headerElement = page.locator('h2[class=ng-star-inserted]');
    await expect(headerElement).toHaveText('Additional Tools & Resources');
  });
  
  test('User cannot login with invalid credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');
    
    // Use invalid credentials
    await page.locator('input[formcontrolname="username"]').fill('invalid_user');
    await page.locator('input[formcontrolname="password"]').fill('invalid_password');
    
    // Click login button
    await page.locator('button[type="submit"]').click();
    
    // Check for error message
    const errorMessage = page.locator('.error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText(/invalid|failed|incorrect/i);
  });
});
