import { Given, Then, After, Before, setWorldConstructor, World, Status } from '@cucumber/cucumber';
import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium, firefox, webkit } from '@playwright/test';
import { expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

class CustomWorld extends World {
  browser: Browser | undefined;
  context: BrowserContext | undefined;
  page: Page | undefined;
  browserName: string;

  constructor(options: any) {
    super(options);
    // Default to chromium, but can be overridden with parameters
    this.browserName = this.parameters.browser || 'chromium';
  }

  async init() {
    const headless = this.parameters.headless !== false;
    const traceEnabled = !!this.parameters.trace;
    
    // Launch the appropriate browser based on browserName
    switch (this.browserName) {
      case 'firefox':
        this.browser = await firefox.launch({ headless });
        break;
      case 'webkit':
        this.browser = await webkit.launch({ headless });
        break;
      case 'chromium':
      default:
        this.browser = await chromium.launch({ headless });
        break;
    }
    
    this.context = await this.browser.newContext({
      recordVideo: traceEnabled ? { dir: 'reports/videos/' } : undefined
    });
    
    if (traceEnabled) {
      await this.context.tracing.start({ screenshots: true, snapshots: true });
    }
    
    this.page = await this.context.newPage();
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

setWorldConstructor(CustomWorld);

// Ensure reports directory exists
Before(async function() {
  const dirs = ['reports', 'reports/screenshots', 'reports/videos', 'reports/traces'];
  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }
});

// Take screenshot on failure
After(async function(scenario) {
  if (scenario.result?.status === Status.FAILED && this.page) {
    const screenshotPath = path.join('reports/screenshots', `${scenario.pickle.name.replace(/\s+/g, '-')}-failure.png`);
    await this.page.screenshot({ path: screenshotPath });
    this.attach(fs.readFileSync(screenshotPath), 'image/png');
  }
  
  if (this.context && this.parameters.trace) {
    const tracePath = path.join('reports/traces', `${scenario.pickle.name.replace(/\s+/g, '-')}.zip`);
    await this.context.tracing.stop({ path: tracePath });
  }
  
  await this.cleanup();
});

Given('I navigate to the homepage', async function() {
  if (!this.page) {
    await this.init();
  }
  await this.page!.goto('/');
});

Then('I should see the welcome message', async function() {
  await expect(this.page!.locator('body')).toContainText('Hello, world!');
});

