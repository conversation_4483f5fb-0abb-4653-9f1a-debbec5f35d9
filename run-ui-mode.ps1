# PowerShell script to run tests in UI mode

param (
    [string]$env = "dev",
    [string]$browser = "chromium",
    [switch]$debug = $false
)

# Set environment variable
$env:TEST_ENV = $env

# Build the command
$command = "npx playwright test --ui"

# Add browser project if specified
if ($browser -ne "") {
    $command += " --project=$browser"
}

# Add debug flag if specified
if ($debug) {
    $command = $command -replace "--ui", "--debug"
}

# Add config file
$command += " --config=playwright-ui.config.ts"

# Display the command
Write-Host "Running: $command"
Write-Host "Environment: $env"
Write-Host "Browser: $browser"
Write-Host "Debug Mode: $debug"

# Execute the command
Invoke-Expression $command
