Feature: User Registration
	@RegistrationSanity @SanityTest @Regression
	Scenario: A new user will try to register under Compsych to GRO
    Given access to GRO login page "/groWeb/login/login.xhtml"
    When we try to click on the Register link
    Then we should see the registration page
    When we try to enter "cpwellbeing" for the orgnization id and submit
    Then we should be taken to the Registration Profile page
    When we fill up the Registration form
    And we continue to the Registration Personalization page
    Then we try to logout and login using the newly created user
    Then we do clean up the registered user
