import { FullConfig } from '@playwright/test';

/**
 * Global teardown function that runs after all tests
 */
async function globalTeardown(config: FullConfig) {
  console.log('Starting global teardown...');
  
  // You can add global teardown logic here, such as:
  // - Cleaning up test data
  // - Closing connections
  // - Generating additional reports
  
  console.log('Global teardown complete.');
}

export default globalTeardown;
