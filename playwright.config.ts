import { PlaywrightTestConfig } from '@playwright/test';

const config: PlaywrightTestConfig = {
  testDir: './features',
  timeout: 30000,
  use: {
    baseURL: 'https://www.guidanceresources-stg.compsych-ad.int',
    headless: false, // Changed to false to make browsers visible
    viewport: { width: 1280, height: 720 },
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'on-first-retry'
  },
  reporter: [
    ['html', {
      outputFolder: 'playwright-report',
      open: 'on-failure'
    }],
    ['json', { outputFile: 'test-results/test-results.json' }]
  ],
  projects: [
    {
      name: 'chromium',
      use: { browserName: 'chromium' }
    },
    {
      name: 'firefox',
      use: { browserName: 'firefox' }
    },
    {
      name: 'webkit',
      use: { browserName: 'webkit' }
    }
  ]
};

export default config;

