# GRO E2E Testing Framework

This project contains end-to-end tests for the GRO application using Playwright and Cucumber.

## Features

- User Registration
- Login
- Multi-Account Login
- UI Mode for visual test execution and debugging
- More features to be added...

## Setup

1. Install dependencies:

   ```bash
   npm install
   ```

2. Install Playwright browsers:

   ```bash
   npx playwright install
   ```

## Test Environment Configuration

The tests can be run against different environments by setting the `TEST_ENV` environment variable. The available environments are:

- `dev` (default)
- `qa`

The configuration for each environment is stored in the `config/test-env.<env>.json` files.

## Test Data

### Login Credentials

The test credentials are stored in `config/test-credentials.json`. This file contains a list of user accounts that can be used for testing, including:

- Username
- WebID
- Password

These credentials can be accessed in the tests using the helper functions:

- `getCredentialByWebId(webId)`: Get credentials by WebID
- `getCredentialByUsername(username)`: Get credentials by username
- `loginWithWebId(page, webId)`: Login using a specific WebID

## Parallel Execution

This project supports parallel test execution to speed up the test runs. When running tests in parallel:

- Tests run in headless mode for better performance
- Each scenario runs in its own browser instance
- By default, 3 scenarios run in parallel, but this can be configured
- Test reports are generated in the same way as sequential runs

Parallel execution is particularly useful for:

- Running regression tests
- Running tests across multiple browsers
- CI/CD pipelines where speed is important

To adjust the number of parallel workers, you can set the `CUCUMBER_PARALLEL` environment variable or use the `-workers` parameter in the PowerShell script.

## UI Mode

This project includes Playwright's UI Mode, which provides a visual interface for running and debugging tests. UI Mode offers:

- Interactive test execution
- Visual test explorer
- Time travel debugging
- Network request inspection
- Step-by-step execution
- Visual tracing
- Screenshot comparison

UI Mode is particularly useful for:

- Developing new tests
- Debugging failing tests
- Understanding test flow
- Inspecting network requests
- Visualizing test execution

To use UI Mode, run one of the UI scripts:

```bash
npm run ui                # Run all tests in UI mode
npm run ui:chromium       # Run tests in UI mode using Chromium
```

Or use the PowerShell script:

```powershell
./run-ui-mode.ps1 -env dev -browser chromium
```

## Running Tests

### Using npm scripts

Run all tests:

```bash
npm test
```

Run tests in a specific browser:

```bash
npm run test:chromium
npm run test:firefox
npm run test:webkit
```

Run tests in a specific environment:

```bash
npm run test:dev
npm run test:qa
```

Run specific feature tests:

```bash
npm run test:login
npm run test:registration
```

Run tests by tag:

```bash
npm run test:sanity        # Run all tests tagged with @SanityTest
npm run test:smoke         # Run all tests tagged with @Smoke
npm run test:regression    # Run all tests tagged with @Regression
npm run test:wip           # Run all tests tagged with @WIP (Work in Progress)
npm run test:multi-account # Run multi-account login tests
```

Run tests in parallel:

```bash
npm run test:parallel           # Run all tests in parallel
npm run test:parallel:chromium  # Run all tests in parallel using Chromium
npm run test:parallel:firefox   # Run all tests in parallel using Firefox
npm run test:parallel:webkit    # Run all tests in parallel using WebKit
npm run test:parallel:all       # Run all tests in parallel in all browsers
npm run test:parallel:sanity    # Run sanity tests in parallel
npm run test:parallel:smoke     # Run smoke tests in parallel
npm run test:parallel:regression # Run regression tests in parallel
```

Run tests in UI mode:

```bash
npm run ui                # Run all tests in UI mode
npm run ui:chromium       # Run tests in UI mode using Chromium
npm run ui:firefox        # Run tests in UI mode using Firefox
npm run ui:webkit         # Run tests in UI mode using WebKit
npm run ui:debug          # Run tests in debug mode
```

### Using PowerShell script

The `run-tests.ps1` script provides more flexibility for running tests:

```powershell
./run-tests.ps1 -env dev -browser chromium -tags "@LoginSanity"
```

For parallel execution:

```powershell
./run-tests.ps1 -env dev -browser chromium -tags "@SanityTest" -parallel -workers 4
```

Parameters:

- `-env`: The test environment (default: "dev")
- `-browser`: The browser to use (default: "chromium")
- `-tags`: Cucumber tags to filter tests (optional)
- `-parallel`: Run tests in parallel (default: false)
- `-workers`: Number of parallel workers (default: 3)
- `-report`: Generate HTML reports after test execution (default: false)
- `-openReport`: Open the HTML report after generation (default: false)

## Test Reports

The project supports multiple types of HTML reports to provide detailed test results:

### Basic HTML Report

The basic HTML report is generated automatically when running tests:

```bash
npm run test:report        # Run tests and generate reports
npm run test:chromium:report # Run tests in Chromium and generate reports
npm run test:parallel:report # Run tests in parallel and generate reports
```

### Opening Reports

You can open the generated reports using:

```bash
npm run open:report        # Open the single HTML report
npm run open:multi-report  # Open the consolidated HTML report
```

### Report Features

The HTML reports include:

- Test results with pass/fail status
- Test duration and timestamps
- Screenshots of failed tests
- Environment information
- Browser and platform details
- Detailed error messages
- Feature and scenario descriptions

### Using PowerShell Script

You can also generate reports using the PowerShell script:

```powershell
./run-tests.ps1 -env dev -browser chromium -tags "@SanityTest" -report -openReport
```

For UI mode:

```powershell
./run-ui-mode.ps1 -env dev -browser chromium
./run-ui-mode.ps1 -env dev -browser firefox
./run-ui-mode.ps1 -env dev -debug
```

## Project Structure

- `features/`: Contains feature files and step definitions
  - `*.feature`: Cucumber feature files
  - `steps/`: Step definitions
  - `support/`: Support files and helpers
    - `helpers.ts`: Helper functions for tests
    - `reporter.ts`: HTML report configuration
- `ui-mode-tests/`: Playwright tests for UI mode
  - `*.spec.ts`: Test files that can be run in UI mode
- `config/`: Test configuration
  - `test-env.*.json`: Environment-specific configuration
  - `test-credentials.json`: Test user credentials
- `reports/`: Test reports (generated after running tests)
  - `*.json`: JSON reports from Cucumber
  - `*-html-report.html`: Individual HTML reports
  - `multi-report/`: Consolidated HTML report
- `generate-reports.ts`: Script to generate HTML reports
- `run-tests.ps1`: PowerShell script for running tests
- `run-ui-mode.ps1`: PowerShell script for running UI mode
- `playwright-ui.config.ts`: Playwright configuration for UI mode
- `global-setup.ts`: Global setup for Playwright tests
- `global-teardown.ts`: Global teardown for Playwright tests

## Adding New Tests

### Cucumber Tests

1. Create a new feature file in the `features/` directory
2. Implement step definitions in the `features/steps/` directory
3. Add any helper functions to `features/support/helpers.ts`
4. Update the test environment configuration if needed

### UI Mode Tests

1. Create a new spec file in the `ui-mode-tests/` directory
2. Follow the Playwright test format with `test` and `expect` functions
3. Reuse helper functions from `features/support/helpers.ts`
4. Run the test in UI mode with `npm run ui` or `./run-ui-mode.ps1`
