import { generateReport, generateMultiReport } from './features/support/reporter';
import * as fs from 'fs';
import * as path from 'path';

// Get all JSON report files
const reportsDir = path.join(__dirname, 'reports');
const jsonFiles = fs.readdirSync(reportsDir)
  .filter(file => file.endsWith('.json'))
  .map(file => path.join(reportsDir, file));

// Generate individual reports
jsonFiles.forEach(jsonFile => {
  generateReport(jsonFile);
});

// Generate consolidated report
generateMultiReport();

console.log('All reports generated successfully!');
