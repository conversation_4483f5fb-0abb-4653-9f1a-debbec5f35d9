import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { login, getEnv } from '../support/helpers';

Given('access to GRO login page {string}', async function (url) {
    if (!this.page) {
        await this.init();
    }
    await this.page.goto(url);
});

When('we try to click on the Register link', async function () {
    await this.page.locator('#registerTab').click();
});

Then('we should see the registration page', async function () {
    await expect(this.page.locator('#registerTab')).toBeVisible();
});

When('we try to enter {string} for the orgnization id and submit', async function (webId) {
    await this.page.locator('#webId').fill(webId);
    await this.page.locator('#registerButton').click();
});

Then('we should be taken to the Registration Profile page', async function () {
    const header = this.page.locator('#oneColumn > div > h1');
    await expect(header).toHaveText('Registration: Profile');
});

When('we fill up the Registration form', async function () {
    await this.page.locator('#reg1\\:username').fill(getEnv('regUsername'));
    await this.page.locator('#reg1\\:password').fill(getEnv('regPassword'));
    await this.page.locator('#reg1\\:confirmPassword').fill(getEnv('regPassword'));
    await this.page.locator('#reg1\\:emailAddress').fill(getEnv('regEmail'));
    await this.page.locator('#reg1\\:reenterEmailAddress').fill(getEnv('regEmail'));
    await this.page.locator('#reg1\\:security\\:0\\:securityQuestion').selectOption('reg1.securityQuestion.1');
    await this.page.locator('#reg1\\:security\\:0\\:securityAnswer').fill(getEnv('regSecretAns'));
    await this.page.locator('#reg1\\:security\\:1\\:securityQuestion').selectOption('reg1.securityQuestion.2');
    await this.page.locator('#reg1\\:security\\:1\\:securityAnswer').fill(getEnv('regSecretAns'));
    await this.page.locator('#reg1\\:security\\:2\\:securityQuestion').selectOption('reg1.securityQuestion.3');
    await this.page.locator('#reg1\\:security\\:2\\:securityAnswer').fill(getEnv('regSecretAns'));
    await this.page.locator('#reg1\\:coppa\\:0').check();
    await this.page.locator('#reg1\\:btnNext').click();
});

When('we continue to the Registration Personalization page', async function () {
    await this.page.locator('#j_idt506\\:reg2\\:firstName').fill(getEnv('regFirstname'));
    await this.page.locator('#j_idt506\\:reg2\\:lastName').fill(getEnv('regLastname'));
    await this.page.locator('#j_idt506\\:reg2\\:employeeId').fill(getEnv('regEmpId'));
    await this.page.locator('#j_idt506\\:reg2\\:dateOfBirthPopupButton').click();
    
    // Click multiple times to navigate to the desired date
    for (let i = 0; i < 11; i++) {
        await this.page.locator(':nth-child(4) > .rf-cal-tl-btn').click();
    }
    
    await this.page.locator('#j_idt506\\:reg2\\:dateOfBirthDayCell6').click({ force: true });
    await this.page.locator('#j_idt506\\:reg2\\:profilePhone').fill(getEnv('regPhoneNo'));
    await this.page.locator('#j_idt506\\:reg2\\:zipCode').fill(getEnv('regZipcode'));
    await this.page.locator('#j_idt506\\:reg2\\:terms').check();
    await this.page.locator('.submit').click();
});

Then('we try to logout and login using the newly created user', async function () {
    await this.page.locator('.navMenu > :nth-child(5) > .clicker').click(); // logout after registration
    await login(this.page, getEnv('regUsername'), getEnv('regPassword'));
    
    await expect(this.page).toHaveURL(new RegExp('groNg/#/home'));
    const headerElement = this.page.locator('h2[class=ng-star-inserted]');
    await expect(headerElement).toHaveText('Additional Tools & Resources');
    
    await this.page.locator('.navMenu > :nth-child(5) > .clicker').click();
});

Then('we do clean up the registered user', async function () {
    // Clean up the registered user
    await this.page.goto('https://www.guidanceresources-stg.compsych-ad.int/groWeb/admin/login/login.xhtml');
    await this.page.locator('#login\\:userName').fill('hmangune');
    await this.page.locator('#login\\:password').fill('Testtest1');
    await this.page.locator('#login\\:loginButton').click();
    await this.page.locator(':nth-child(3) > .clicker').click();
    await this.page.locator('#j_idt218\\:userSearch\\:username').fill('testauto');
    await this.page.locator('#j_idt218\\:userSearch\\:searchUserButton').click();
    await this.page.locator('#j_idt218\\:userResults\\:j_idt240\\:0\\:username > a').click();
    await this.page.locator('#j_idt216\\:user\\:deleteUser').click();
    await this.page.locator('#j_idt216\\:user\\:yesButton1').click();
    await this.page.locator('.glyphicon').click();
    await this.page.locator('#navMenu > li > a').click();
});
